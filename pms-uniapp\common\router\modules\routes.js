const routes = [
	{
	 path: "/pages/login/login",
	 name: 'login',
		 meta: {
			 title: '登录',
		 },
	},
	{
		path: "/pages/login/loginOauth2",
		name: 'oauth2Login',
		meta: {
			title: 'oauth2登录',
		},
	},
	{
        //注意：path必须跟pages.json中的地址对应，最前面别忘了加'/'哦
      path: '/pages/index/index',
      name: 'index',
        meta: {
	        title: '主页',
	    },
    },
	{
	    //注意：path必须跟pages.json中的地址对应，最前面别忘了加'/'哦
	  path: '/pages/home/<USER>',
	  //aliasPath:'/',  //对于h5端你必须在首页加上aliasPath并设置为/
	  name: 'home',
	    meta: {
	        title: '首页',
	    },
	},
    {
	    path: '/pages/user/people',
        name: 'people',
        meta: {
	        title: '个人中心',
	    },
	},
	{
	    path: '/pages/user/userdetail',
	    name: 'userdetail',
	    meta: {
	        title: '个人详情',
	    },
	},
	{
	    path: '/pages/user/useredit',
	    name: 'useredit',
	    meta: {
	        title: '个人编辑',
	    },
	},
	{
	    path: '/pages/user/userexit',
	    name: 'userexit',
	    meta: {
	        title: '退出',
	    },
	},
	{
	    path: '/pages/user/location',
	    name: 'location',
	    meta: {
	        title: '定位',
	    },
	},
	{
	    path: '/pages/common/exit',
	    name: 'exit',
	    meta: {
	        title: '退出',
	    },
	},
	{
	    path: '/pages/common/success',
	    name: 'success',
	    meta: {
	        title: 'success',
	    },
	},{
	  path: '/pages/addressbook/address-book',
	  name: 'addressBook',
	    meta: {
	        title: 'addressBook',
	    },
	},
	{
	  path: '/pages/addressbook/level-address-book',
	  name: 'levelAddressBook',
	    meta: {
	        title: 'levelAddressBook',
	    },
	},
	{
	  path: '/pages/addressbook/member',
	  name: 'member',
	    meta: {
	        title: 'member',
	    },
	},
	{
	  path: '/pages/addressbook/address-detail',
	  name: 'addressDetail',
	    meta: {
	        title: 'addressDetail',
	    },
	},
	{
	    path: '/pages/annotation/annotationList',
	    name: 'annotationList',
	    meta: {
	        title: '通知公告',
	    },
	},
	{
	    path: '/pages/annotation/annotationDetail',
	    name: 'annotationDetail',
	    meta: {
	        title: '通知详情',
	    },
	},
	{
	    path: '/pages/common/helloWorld',
	    name: 'helloWorld',
	    meta: {
	        title: 'helloWorld',
	    },
	},
	{
	    path: '/pages/userInfo/userInfo',
	    name: 'userInfo',
	    meta: {
	        title: '人员信息',
	    },
	},
	{
	    path: '/pages/customerInfo/customerInfo',
	    name: 'customerInfo',
	    meta: {
	        title: '客户信息',
	    },
	},
	{
	    path: '/pages/salesData/salesData',
	    name: 'salesData',
	    meta: {
	        title: '发货数据',
	    },
	},
	{
	    path: '/pages/payData/payData',
	    name: 'payData',
	    meta: {
	        title: '回款数据',
	    },
	},
	{
	    path: '/pages/userInfo/userInfoDetail',
	    name: 'userInfoDetail',
	    meta: {
	        title: '人员详情',
	    },
	},
	{
	    path: '/pages/customerInfo/customerInfoDetail',
	    name: 'customerInfoDetail',
	    meta: {
	        title: '客户详情',
	    },
	},
	{
	    path: '/pages/travelExpense/travelExpense',
	    name: 'travelExpense',
	    meta: {
	        title: '差旅费',
	    },
	},
	{
	    path: '/pages/trackingReport/trackingReport',
	    name: 'trackingReport',
	    meta: {
	        title: '每日项目跟踪',
	    },
	},
	{
	    path: '/pages/trackingReport/trackingReportDetail',
	    name: 'trackingReportDetail',
	    meta: {
	        title: '每日项目跟踪',
	    },
	},
	{
	    path: '/pages/salesDataEntry/salesDataEntry',
	    name: 'salesDataEntry',
	    meta: {
	        title: '销售数据录入',
	    },
	},
	{
	    path: '/pages/opocUserList/opocUserList',
	    name: 'opocUserList',
	    meta: {
	        title: '促销员列表',
	    },
	},
	{
	    path: '/pages/signIn/signIn',
	    name: 'signIn',
	    meta: {
	        title: '签到',
	    },
	},
	{
	    path: '/pages/dailyWorkReport/dailyWorkReport',
	    name: 'dailyWorkReport',
	    meta: {
	        title: '工作日报',
	    },
	},
	{
	    path: '/pages/workMoments/workMoments',
	    name: 'workMoments',
	    meta: {
	        title: '工作朋友圈',
	    },
	},
	{
	    path: '/pages/customerVisit/customerVisit',
	    name: 'customerVisit',
	    meta: {
	        title: '客户拜访',
	    },
	},
	{
	    path: '/pages/customerVisit/visitDetail',
	    name: 'visitDetail',
	    meta: {
	        title: '拜访计划',
	    },
	},
	{
		path: '/pages/salerList/salerList',
	    name: 'salerList',
	    meta: {
	        title: '业务员列表',
	    },
	},
	{
		path: '/pages/costList/costList',
	    name: 'costList',
	    meta: {
	        title: '费用申请',
	    },
	},
	{
		path: '/pages/brandPromotion/brandPromotion',
	    name: 'brandPromotion',
	    meta: {
	        title: '品牌宣传',
	    },
	},
	{
		path: '/pages/SFASummary/SFASummary',
	    name: 'SFASummary',
	    meta: {
	        title: '每日情况汇总',
	    },
	},
	{
		path: '/pages/SFASummary/SFASummaryDetail',
	    name: 'SFASummaryDetail',
	    meta: {
	        title: '每日情况汇总详情',
	    },
	}
]
export default routes