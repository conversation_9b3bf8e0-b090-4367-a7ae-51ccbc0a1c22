<template>
    <view>
        <cu-custom :bgColor="NavBarColor" isBack>
            <block slot="backText">返回</block>
            <block slot="content">{{ userName }}</block>
        </cu-custom>
    </view>
</template>

<script>
import configService from '@/common/service/config.service.js';

export default {
    data() {
        return {
            userName: "",
            detailInfo:{}
        }
    },

    async onLoad(options) {
        console.log("🚀 ~ onLoad ~ options:", options)
        this.userName = options.name;
        try {
            const result = await this.$http.get(`/pm/clockIn/overviewDetail?userCode=${options.userCode}&date=${options.date}`);
            if (result.data.code == 200) {
                this.detailInfo = result.data.result;
                console.log("🚀 ~ this.detailInfo", this.detailInfo)
            } else {
                uni.showToast({
                    title: result.message || '获取详情失败',
                    icon: 'none'
                });
            }
        } catch (error) {
            console.error('获取详情失败:', error);
            uni.showToast({
                title: '获取详情失败',
                icon: 'none'
            });
        }
    },

    methods: {},
}
</script>

<style lang="scss" scoped></style>
