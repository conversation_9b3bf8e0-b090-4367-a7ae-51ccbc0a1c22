<template>
	<view class="customer-visit-container">
		<!-- 顶部导航 -->
		<cu-custom :bgColor="NavBarColor" isBack>
			<block slot="backText">返回</block>
			<block slot="content">客户拜访</block>
		</cu-custom>

		<!-- 主内容区 -->
		<view class="main-content">
			<!-- 今日状态卡片 -->
			<!-- <view class="status-card">
				<view class="status-title">
					<text class="cuIcon-calendar text-white margin-right-xs"></text>
					<text>今日拜访状态</text>
				</view>
				<view class="status-content">
					<view class="status-item">
						<view class="status-value">{{ todayStats.planned }}</view>
						<view class="status-label">计划拜访</view>
					</view>
					<view class="status-item">
						<view class="status-value">{{ todayStats.completed }}</view>
						<view class="status-label">已完成</view>
					</view>
					<view class="status-item">
						<view class="status-value">{{ todayStats.inProgress }}</view>
						<view class="status-label">进行中</view>
					</view>
					<view class="status-item">
						<view class="status-value">{{ todayStats.canceled }}</view>
						<view class="status-label">已取消</view>
					</view>
				</view>
			</view> -->

			<!-- 今日拜访计划 -->
			<view class="card">
				<view class="card-header">
					<view class="flex align-center">
						<text class="cuIcon-group text-blue margin-right-xs"></text>
						<text>今日拜访计划</text>
					</view>
					<text class="cuIcon-add text-blue" @tap="showAddVisitModal"></text>
				</view>
				<view class="card-body">
					<view v-if="todayVisits.length === 0" class="empty-state">
						<text class="cuIcon-calendar text-gray"></text>
						<text class="empty-text">今日暂无拜访计划</text>
					</view>
					<view v-else>
						<view v-for="(visit, index) in todayVisits" :key="visit.id" class="visit-item"
							@tap="viewVisitDetail(visit)">
							<view class="visit-avatar">
								{{ visit.customerName.charAt(0) }}
							</view>
							<view class="visit-info">
								<view class="visit-name">{{ visit.customerName }}</view>
								<view class="visit-meta">
									<text class="cuIcon-time text-gray margin-right-xs"></text>
									<text>{{ visit.visitTime }}</text>
									<text class="cuIcon-location text-gray margin-left-sm margin-right-xs"></text>
									<text>{{ visit.customerAddress }}</text>
								</view>
								<view class="visit-purpose margin-top-xs">
									<text class="text-sm text-gray">{{ visit.visitPurpose }}</text>
								</view>
								<!-- 分销商和体系信息 -->
								<view class="visit-business-info margin-top-xs"
									v-if="visit.distributorName || visit.systemName">
									<view v-if="visit.distributorName" class="business-item">
										<text class="cuIcon-group text-blue margin-right-xs"></text>
										<text class="business-label">分销商:</text>
										<text class="business-value">{{ visit.distributorName }}</text>
									</view>
									<view v-if="visit.systemName" class="business-item margin-top-xs">
										<text class="cuIcon-settings text-orange margin-right-xs"></text>
										<text class="business-label">体系:</text>
										<text class="business-value">{{ visit.systemName }}</text>
									</view>
								</view>
							</view>
							<view class="visit-actions">
								<view class="visit-status" :class="getStatusClass(visit.visitStatus)">
									{{ getStatusText(visit.visitStatus) }}
								</view>
								<!-- <view v-if="visit.visitStatus === 1" class="action-buttons margin-top-xs">
									<button class="cu-btn sm bg-green" @tap.stop="startVisit(visit)">开始</button>
								</view>
								<view v-else-if="visit.visitStatus === 2" class="action-buttons margin-top-xs">
									<button class="cu-btn sm bg-blue" @tap.stop="finishVisit(visit)">完成</button>
								</view> -->
							</view>
						</view>
					</view>
					<button class="add-button margin-top" @tap="showAddVisitModal">
						<text class="cuIcon-add margin-right-xs"></text>
						<text>新增拜访计划</text>
					</button>
				</view>
			</view>

			<!-- 工作轨迹 -->
			<view class="card">
				<view class="card-header">
					<view class="flex align-center">
						<text class="cuIcon-location text-orange margin-right-xs"></text>
						<text>今日工作轨迹</text>
					</view>
					<text class="cuIcon-refresh text-orange" @tap="refreshTrack"></text>
				</view>
				<view class="card-body">
					<!-- 地图容器 -->
					<view class="map-container">
						<map id="workTrackMap" :longitude="mapCenter.longitude" :latitude="mapCenter.latitude"
							:scale="mapScale" :markers="mapMarkers" :polyline="mapPolyline" :show-location="true"
							class="work-map"></map>

						<!-- 地图控制按钮 -->
						<!-- <view class="map-controls">
							<view class="control-btn" @tap="refreshTrack">
								<text class="cuIcon-refresh text-white"></text>
							</view>
							<view class="control-btn" @tap="centerToVisits">
								<text class="cuIcon-group text-white"></text>
							</view>
							<view class="control-btn" @tap="showMarkerList">
								<text class="cuIcon-list text-white"></text>
							</view>
							<view class="control-btn" @tap="testGeneratePolyline">
								<text class="cuIcon-link text-white"></text>
							</view>
						</view> -->
					</view>

					<!-- 轨迹统计 -->
					<view class="track-info">
						<view class="track-data">
							<view class="track-value">{{ trackStats.distance }}</view>
							<view class="track-label">行程(km)</view>
						</view>
						<view class="track-data">
							<view class="track-value">{{ trackStats.duration }}</view>
							<view class="track-label">时长(h)</view>
						</view>
						<view class="track-data">
							<view class="track-value">{{ mapMarkers.length }}</view>
							<view class="track-label">标记点</view>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 新增拜访计划弹窗 -->
		<view v-if="showAddModal" class="modal-overlay" @tap="hideAddModal">
			<view class="modal-content" @tap.stop>
				<view class="modal-header">
					<text class="modal-title">新增拜访计划</text>
					<text class="cuIcon-close text-gray" @tap="hideAddModal"></text>
				</view>
				<view class="modal-body">
					<!-- 客户类型选择 -->
					<view class="form-group">
						<text class="form-label">客户类型</text>
						<picker mode="selector" :range="customerTypeList" range-key="name"
							:value="getCustomerTypeIndex()" @change="onCustomerTypeChange">
							<view class="picker-input">
								{{ selectedCustomerType ? selectedCustomerType.name : '请选择客户类型' }}
								<text class="cuIcon-right text-gray"></text>
							</view>
						</picker>
					</view>

					<!-- 客户选择 -->
					<view class="form-group" v-if="selectedCustomerType && selectedCustomerType.code === '客户'">
						<text class="form-label">客户名称</text>
						<view class="customer-search-container">
							<!-- 搜索输入框 -->
							<view v-if="!selectedCustomer || showCustomerList" class="search-input-wrapper"
								@tap="focusInputBox">
								<u--input ref="customerInput" v-model="customerSearchKeyword" placeholder="请输入客户名称搜索"
									class="customer-search-input" @input="onCustomerSearch" @focus="onInputFocus"
									@blur="onInputBlur" :disabled="false" confirm-type="search" :adjust-position="false"
									:focus="inputFocused" />
								<text class="cuIcon-search text-gray search-icon"></text>
							</view>

							<!-- 已选择的客户显示 -->
							<view v-if="selectedCustomer && !showCustomerList" class="selected-customer"
								@tap="editCustomerSelection">
								<view class="selected-info">
									<text class="selected-name">{{ selectedCustomer.name }}</text>
									<text class="selected-code">{{ selectedCustomer.code }}</text>
								</view>
								<text class="cuIcon-close text-gray" @tap.stop="clearSelectedCustomer"></text>
							</view>

							<!-- 客户搜索结果列表 -->
							<view v-if="showCustomerList && customerSearchResults.length > 0"
								class="customer-list-dropdown">
								<view v-for="customer in customerSearchResults" :key="customer.customerCode"
									class="customer-item" @tap="selectCustomer(customer)">
									<view class="customer-info">
										<text class="customer-name">{{ customer.name }}</text>
										<text class="customer-code">{{ customer.code }}</text>
									</view>
								</view>
							</view>

							<!-- 无搜索结果 -->
							<view v-if="showCustomerList && customerSearchKeyword && customerSearchResults.length === 0"
								class="no-results">
								<text class="text-gray">未找到相关客户</text>
							</view>
						</view>
					</view>

					<!-- 体系选择 -->
					<view class="form-group" v-if="selectedCustomerType && selectedCustomerType.code === '体系'">
						<text class="form-label">体系名称</text>
						<view class="customer-search-container">
							<!-- 搜索输入框 -->
							<view v-if="!selectedSystem || showSystemList" class="search-input-wrapper"
								@tap="focusSystemInputBox">
								<u--input ref="systemInput" v-model="systemSearchKeyword" placeholder="请输入体系名称搜索"
									class="customer-search-input" @input="onSystemSearch" @focus="onSystemInputFocus"
									@blur="onSystemInputBlur" :disabled="false" confirm-type="search"
									:adjust-position="false" :focus="systemInputFocused" />
								<text class="cuIcon-search text-gray search-icon"></text>
							</view>

							<!-- 已选择的体系显示 -->
							<view v-if="selectedSystem && !showSystemList" class="selected-customer"
								@tap="editSystemSelection">
								<view class="selected-info">
									<text class="selected-name">{{ selectedSystem.name }}</text>
									<text class="selected-code">{{ selectedSystem.code }}</text>
								</view>
								<text class="cuIcon-close text-gray" @tap.stop="clearSelectedSystem"></text>
							</view>

							<!-- 体系搜索结果列表 -->
							<view v-if="showSystemList && systemSearchResults.length > 0"
								class="customer-list-dropdown">
								<view v-for="system in systemSearchResults" :key="system.name" class="customer-item"
									@tap="selectSystem(system)">
									<view class="customer-info">
										<text class="customer-name">{{ system.name }}</text>
										<text class="customer-code">{{ system.code }}</text>
									</view>
								</view>
							</view>

							<!-- 无搜索结果 -->
							<view v-if="showSystemList && systemSearchKeyword && systemSearchResults.length === 0"
								class="no-results">
								<text class="text-gray">未找到相关体系</text>
							</view>
						</view>
					</view>

					<!-- 分销商选择 -->
					<view class="form-group" v-if="selectedCustomerType && selectedCustomerType.code === '分销商'">
						<text class="form-label">分销商名称</text>
						<view class="customer-search-container">
							<!-- 搜索输入框 -->
							<view v-if="!selectedDistributor || showDistributorList" class="search-input-wrapper"
								@tap="focusDistributorInputBox">
								<u--input ref="distributorInput" v-model="distributorSearchKeyword"
									placeholder="请输入分销商名称搜索" class="customer-search-input" @input="onDistributorSearch"
									@focus="onDistributorInputFocus" @blur="onDistributorInputBlur" :disabled="false"
									confirm-type="search" :adjust-position="false" :focus="distributorInputFocused" />
								<text class="cuIcon-search text-gray search-icon"></text>
							</view>

							<!-- 已选择的分销商显示 -->
							<view v-if="selectedDistributor && !showDistributorList" class="selected-customer"
								@tap="editDistributorSelection">
								<view class="selected-info">
									<text class="selected-name">{{ selectedDistributor.name }}</text>
									<text class="selected-code">{{ selectedDistributor.code }}</text>
								</view>
								<text class="cuIcon-close text-gray" @tap.stop="clearSelectedDistributor"></text>
							</view>

							<!-- 分销商搜索结果列表 -->
							<view v-if="showDistributorList && distributorSearchResults.length > 0"
								class="customer-list-dropdown">
								<view v-for="distributor in distributorSearchResults" :key="distributor.name"
									class="customer-item" @tap="selectDistributor(distributor)">
									<view class="customer-info">
										<text class="customer-name">{{ distributor.name }}</text>
										<text class="customer-code">{{ distributor.code }}</text>
									</view>
								</view>
							</view>

							<!-- 无搜索结果 -->
							<view
								v-if="showDistributorList && distributorSearchKeyword && distributorSearchResults.length === 0"
								class="no-results">
								<text class="text-gray">未找到相关分销商</text>
							</view>
						</view>
					</view>
					<view class="form-group" v-if="selectedCustomerType && selectedCustomerType.code === '意向客户'">
						<text class="form-label">意向客户</text>
						<u--input v-model="visitForm.customerName" placeholder="请输入" class="customer-search-input" />
					</view>

					<view class="form-group">
						<text class="form-label">拜访日期</text>
						<picker mode="date" :value="visitForm.visitDate" @change="onDateChange">
							<view class="picker-input">
								{{ visitForm.visitDate || '请选择日期' }}
								<text class="cuIcon-right text-gray"></text>
							</view>
						</picker>
					</view>

					<view class="form-group">
						<text class="form-label">拜访时间</text>
						<picker mode="time" :value="visitForm.visitTime" @change="onTimeChange">
							<view class="picker-input">
								{{ visitForm.visitTime || '请选择时间' }}
								<text class="cuIcon-right text-gray"></text>
							</view>
						</picker>
					</view>

					<!-- <view class="form-group">
						<text class="form-label">拜访地点</text>
						<view class="location-selector" @tap="showLocationModal">
							<view class="location-input">
								<text v-if="visitForm.visitAddress" class="location-text">{{ visitForm.visitAddress }}-{{visitForm.addressName}}</text>
								<text v-else class="location-placeholder">请选择拜访地点</text>
								<text class="cuIcon-location text-gray"></text>
							</view>
						</view>
					</view> -->

					<view class="form-group">
						<text class="form-label">拜访目的</text>
						<view class="purpose-checkbox-container">
							<view v-for="purpose in visitPurposeList" :key="purpose.purposeCode"
								class="purpose-checkbox-item" @tap="togglePurpose(purpose)">
								<view class="checkbox-wrapper">
									<view class="checkbox"
										:class="{ 'checked': selectedPurposes.includes(purpose.purposeCode) }">
										<text v-if="selectedPurposes.includes(purpose.purposeCode)"
											class="cuIcon-check text-white"></text>
									</view>
									<text class="purpose-text">{{ purpose.purposeName }}</text>
								</view>
							</view>
							<view v-if="selectedPurposes.length === 0" class="purpose-placeholder">
								请选择拜访目的
							</view>
						</view>
					</view>



				</view>
				<view class="modal-footer">
					<button class="cu-btn line-gray" @tap="hideAddModal">取消</button>
					<button class="cu-btn bg-blue" @tap="submitVisit" :disabled="submitting">
						{{ submitting ? '提交中...' : '确定' }}
					</button>
				</view>
			</view>
		</view>

	</view>
</template>

<script>
export default {
	data() {
		return {
			// 今日统计数据
			todayStats: {
				planned: 0,
				completed: 0,
				inProgress: 0,
				canceled: 0
			},

			// 今日拜访列表
			todayVisits: [],

			// 工作轨迹数据
			trackStats: {
				distance: '0.0',
				duration: '0.0',
				locations: 0
			},

			// 地图相关
			mapCenter: {
				longitude: 104.195397, // 中国中心经度
				latitude: 35.86166     // 中国中心纬度
			},
			mapScale: 5, // 调整为全国视图
			mapMarkers: [],
			mapPolyline: [],
			pointList: [],
			// 弹窗控制
			showAddModal: false,
			submitting: false,

			// 表单数据
			visitForm: {
				customerCode: '',
				customerName: '',
				visitDate: '',
				visitTime: '',
				visitAddress: '',
				visitPurpose: '',
				distributorName: '', // 分销商名称
				distributorId: '', // 分销商ID
				systemName: '', // 体系名称
				systemId: '', // 体系ID
			},

			// 客户类型列表
			customerTypeList: [
				{ code: '客户', name: '客户' },
				{ code: '体系', name: '体系' },
				{ code: '分销商', name: '分销商' },
				{ code: '意向客户', name: '意向客户' },
			],
			selectedCustomerType: null, // 选中的客户类型

			// 客户列表
			customerList: [],
			selectedCustomer: null,

			// 分销商和体系列表
			businessDistributorList: [], // 分销商列表
			businessSystemBaseList: [], // 体系列表
			selectedDistributor: null, // 选中的分销商
			selectedSystem: null, // 选中的体系

			// 拜访目的列表
			visitPurposeList: [],
			selectedPurposes: [], // 改为数组存储多选的目的

			// 客户搜索相关
			customerSearchKeyword: '',
			customerSearchResults: [],
			showCustomerList: false,
			searchTimer: null,
			inputFocused: false,

			// 体系搜索相关
			systemSearchKeyword: '',
			systemSearchResults: [],
			showSystemList: false,
			systemSearchTimer: null,
			systemInputFocused: false,

			// 分销商搜索相关
			distributorSearchKeyword: '',
			distributorSearchResults: [],
			showDistributorList: false,
			distributorSearchTimer: null,
			distributorInputFocused: false,

			// 高德地图配置
			amapKey: 'bf98482bb3a3ee9c97a371ffe8fbc9c2',

			// 状态映射
			statusMap: {
				1: { text: '待拜访', class: 'status-planned' },
				2: { text: '进行中', class: 'status-progress' },
				3: { text: '已完成', class: 'status-completed' },
				4: { text: '已取消', class: 'status-canceled' }
			}
		}
	},

	onLoad() {
		this.initPage();
	},

	onShow() {
		this.refreshData();
	},

	onReady() {
		// 页面渲染完成后检查数据
		console.log('页面渲染完成，拜访目的列表:', this.visitPurposeList);
	},

	methods: {
		// 初始化页面
		async initPage() {
			await this.loadCustomerList();
			await this.loadVisitPurposes();
			await this.loadTodayStats();
			await this.loadTodayVisits();

			// 添加拜访地点标记和连线
			this.addVisitLocationMarkers();
			this.generateVisitPolyline();
		},

		// 刷新数据
		async refreshData() {
			await this.loadTodayStats();
			await this.loadTodayVisits();
			// 刷新拜访地点标记和连线
			this.addVisitLocationMarkers();
			this.generateVisitPolyline();
		},

		// 加载今日统计
		async loadTodayStats() {
			try {
				const response = await this.$http.get('/pm/visit/plan/count-statistics');
				if (response.data.success) {
					this.todayStats = response.data.result;
				}
			} catch (error) {
				console.error('加载统计数据失败:', error);
			}
		},

		// 加载今日拜访计划
		async loadTodayVisits() {
			try {
				const response = await this.$http.get('/pm/visit/plan/today');
				if (response.data.success) {
					this.todayVisits = response.data.result || [];
				}
			} catch (error) {
				console.error('加载拜访计划失败:', error);
			}
		},

		// 加载客户列表
		async loadCustomerList() {
			try {
				const response = await this.$http.get('/pm/visit/plan/getChoiceList', {
					params: {
						type: '客户'
					}
				});
				if (response.data.success) {
					this.customerList = response.data.result || [];
					this.customerSearchResults = this.customerList.slice(0, 10); // 默认显示前10个
				}
			} catch (error) {
				console.error('加载客户列表失败:', error);
			}
		},

		// 加载拜访目的列表
		async loadVisitPurposes() {
			try {
				const response = await this.$http.get('/pm/visit/plan/visit-purposes');
				if (response.data.success) {
					this.visitPurposeList = response.data.result.map(item => {
						return {
							purposeCode: item,
							purposeName: item
						}
					});

				}
			} catch (error) {
				console.error('加载拜访目的列表失败:', error);
			}
		},

		// 加载客户的分销商和体系列表
		async loadBusinessDetail(customerCode, customerName) {
			try {
				const response = await this.$http.get('/pm/business/base/detail', {
					params: {
						code: customerCode,
						name: customerName
					}
				});

				if (response.data.success) {
					const result = response.data.result;
					console.log("🚀 ~ loadBusinessDetail ~ result:", result)
					// 设置分销商列表
					this.businessDistributorList = result.businessDistributor || [];
					// 设置体系列表
					this.businessSystemBaseList = result.businessSystemBase || [];

				} else {
					console.error('加载业务详情失败:', response.data.message);
					// 清空列表
					this.businessDistributorList = [];
					this.businessSystemBaseList = [];
				}
			} catch (error) {
				console.error('加载业务详情异常:', error);
				// 清空列表
				this.businessDistributorList = [];
				this.businessSystemBaseList = [];
			}
		},

		// 客户搜索
		onCustomerSearch() {
			// 清除之前的定时器
			if (this.searchTimer) {
				clearTimeout(this.searchTimer);
			}

			// 设置新的定时器，防抖处理
			this.searchTimer = setTimeout(() => {
				this.searchCustomers();
			}, 300);
		},

		// 搜索客户
		async searchCustomers() {
			const keyword = this.customerSearchKeyword.trim();

			if (!keyword) {
				// 如果没有关键字，显示默认列表
				this.customerSearchResults = this.customerList.slice(0, 10);
				return;
			}

			try {
				console.log('搜索客户，关键字:', keyword);
				const response = await this.$http.get('/pm/visit/plan/getChoiceList', {
					params: {
						type: '客户',
						keyword
					}
				});
				console.log('搜索结果:', response);

				if (response.data.success) {
					this.customerSearchResults = response.data.result || [];
				} else {
					this.customerSearchResults = [];
					console.error('搜索客户失败:', response.data.message);
				}
			} catch (error) {
				console.error('搜索客户异常:', error);
				this.customerSearchResults = [];
			}
		},

		// 选择客户
		async selectCustomer(customer) {
			this.selectedCustomer = customer;
			console.log("🚀 ~ selectCustomer ~ 	this.selectedCustomer :", this.selectedCustomer)
			this.customerSearchKeyword = customer.customerName;
			// 确保给visitForm赋值
			this.visitForm.customerCode = customer.code;
			this.visitForm.customerName = customer.name;
			this.visitForm.customerLongitude = customer.longitude;
			this.visitForm.customerLatitude = customer.latitude;
			this.showCustomerList = false;
			console.log('选择客户，visitForm已更新:', this.visitForm);

			// 选择客户后，加载分销商和体系列表
			await this.loadBusinessDetail(customer.code, customer.name);
		},

		// 清除选择的客户
		clearSelectedCustomer() {
			this.selectedCustomer = null;
			this.customerSearchKeyword = '';
			this.visitForm.customerCode = '';
			this.visitForm.customerName = '';
			this.showCustomerList = true;
		},

		// 编辑客户选择
		editCustomerSelection() {
			this.showCustomerList = true;
			this.customerSearchKeyword = this.selectedCustomer ? this.selectedCustomer.customerName : '';
		},

		// 输入框获得焦点
		onInputFocus() {
			console.log('输入框获得焦点');
			this.inputFocused = true;
			this.showCustomerList = true;
			// 如果没有搜索结果，显示默认列表
			if (this.customerSearchResults.length === 0) {
				this.customerSearchResults = this.customerList.slice(0, 10);
			}
		},

		// 输入框失去焦点
		onInputBlur() {
			console.log('输入框失去焦点');
			this.inputFocused = false;
			// 延迟隐藏，给点击选择留出时间
			setTimeout(() => {
				this.showCustomerList = false;
			}, 200);
		},

		// 点击输入框区域聚焦
		focusInputBox() {
			console.log('点击输入框区域');
			this.inputFocused = true;
			this.showCustomerList = true;
			// 如果没有搜索结果，显示默认列表
			if (this.customerSearchResults.length === 0) {
				this.customerSearchResults = this.customerList.slice(0, 10);
			}
		},

		// ========== 体系搜索相关方法 ==========
		// 体系搜索输入变化
		onSystemSearch() {
			// 清除之前的定时器
			if (this.systemSearchTimer) {
				clearTimeout(this.systemSearchTimer);
			}

			// 设置新的定时器，防抖处理
			this.systemSearchTimer = setTimeout(() => {
				this.searchSystems();
			}, 300);
		},

		// 搜索体系
		async searchSystems() {
			const keyword = this.systemSearchKeyword.trim();

			if (!keyword) {
				// 如果没有关键字，显示默认列表
				this.systemSearchResults = this.businessSystemBaseList.slice(0, 10);
				return;
			}

			try {
				console.log('搜索体系，关键字:', keyword);
				const response = await this.$http.get('/pm/visit/plan/getChoiceList', {
					params: {
						type: '体系',
						keyword
					}
				});
				console.log('体系搜索结果:', response);

				if (response.data.success) {
					this.systemSearchResults = response.data.result || [];
				} else {
					this.systemSearchResults = [];
					console.error('搜索体系失败:', response.data.message);
				}
			} catch (error) {
				console.error('搜索体系异常:', error);
				this.systemSearchResults = [];
			}
		},

		// 选择体系
		selectSystem(system) {
			this.selectedSystem = system;
			console.log("🚀 ~ selectSystem ~ selectedSystem:", this.selectedSystem);
			this.systemSearchKeyword = system.name;
			// 确保给visitForm赋值
			this.visitForm.customerCode = system.code;
			this.visitForm.customerName = system.name;
			this.visitForm.systemName = system.name;
			this.visitForm.systemId = system.id; // 设置体系ID
			this.showSystemList = false;
			console.log('选择体系，visitForm已更新:', this.visitForm);
		},

		// 清除选择的体系
		clearSelectedSystem() {
			this.selectedSystem = null;
			this.systemSearchKeyword = '';
			this.visitForm.systemName = '';
			this.showSystemList = true;
		},

		// 编辑体系选择
		editSystemSelection() {
			this.showSystemList = true;
			this.systemSearchKeyword = this.selectedSystem ? this.selectedSystem.name : '';
		},

		// 体系输入框获得焦点
		onSystemInputFocus() {
			console.log('体系输入框获得焦点');
			this.systemInputFocused = true;
			this.showSystemList = true;
			// 如果没有搜索结果，显示默认列表
			if (this.systemSearchResults.length === 0) {
				this.systemSearchResults = this.businessSystemBaseList.slice(0, 10);
			}
		},

		// 体系输入框失去焦点
		onSystemInputBlur() {
			console.log('体系输入框失去焦点');
			this.systemInputFocused = false;
			// 延迟隐藏，给点击选择留出时间
			setTimeout(() => {
				this.showSystemList = false;
			}, 200);
		},

		// 点击体系输入框区域聚焦
		focusSystemInputBox() {
			console.log('点击体系输入框区域');
			this.systemInputFocused = true;
			this.showSystemList = true;
			// 如果没有搜索结果，显示默认列表
			if (this.systemSearchResults.length === 0) {
				this.systemSearchResults = this.businessSystemBaseList.slice(0, 10);
			}
		},

		// ========== 分销商搜索相关方法 ==========
		// 分销商搜索输入变化
		onDistributorSearch() {
			// 清除之前的定时器
			if (this.distributorSearchTimer) {
				clearTimeout(this.distributorSearchTimer);
			}

			// 设置新的定时器，防抖处理
			this.distributorSearchTimer = setTimeout(() => {
				this.searchDistributors();
			}, 300);
		},

		// 搜索分销商
		async searchDistributors() {
			const keyword = this.distributorSearchKeyword.trim();

			if (!keyword) {
				// 如果没有关键字，显示默认列表
				this.distributorSearchResults = this.businessDistributorList.slice(0, 10);
				return;
			}

			try {
				console.log('搜索分销商，关键字:', keyword);
				const response = await this.$http.get('/pm/visit/plan/getChoiceList', {
					params: {
						type: '分销商',
						keyword
					}
				});
				console.log('分销商搜索结果:', response);

				if (response.data.success) {
					this.distributorSearchResults = response.data.result || [];
				} else {
					this.distributorSearchResults = [];
					console.error('搜索分销商失败:', response.data.message);
				}
			} catch (error) {
				console.error('搜索分销商异常:', error);
				this.distributorSearchResults = [];
			}
		},

		// 选择分销商
		selectDistributor(distributor) {
			this.selectedDistributor = distributor;
			console.log("🚀 ~ selectDistributor ~ selectedDistributor:", this.selectedDistributor);
			this.distributorSearchKeyword = distributor.name;
			// 确保给visitForm赋值
			this.visitForm.customerCode = distributor.code;
			this.visitForm.customerName = distributor.name;
			this.visitForm.distributorName = distributor.name;
			this.visitForm.distributorId = distributor.id; // 设置分销商ID
			this.showDistributorList = false;
			console.log('选择分销商，visitForm已更新:', this.visitForm);
		},

		// 清除选择的分销商
		clearSelectedDistributor() {
			this.selectedDistributor = null;
			this.distributorSearchKeyword = '';
			this.visitForm.distributorName = '';
			this.showDistributorList = true;
		},

		// 编辑分销商选择
		editDistributorSelection() {
			this.showDistributorList = true;
			this.distributorSearchKeyword = this.selectedDistributor ? this.selectedDistributor.name : '';
		},

		// 分销商输入框获得焦点
		onDistributorInputFocus() {
			console.log('分销商输入框获得焦点');
			this.distributorInputFocused = true;
			this.showDistributorList = true;
			// 如果没有搜索结果，显示默认列表
			if (this.distributorSearchResults.length === 0) {
				this.distributorSearchResults = this.businessDistributorList.slice(0, 10);
			}
		},

		// 分销商输入框失去焦点
		onDistributorInputBlur() {
			console.log('分销商输入框失去焦点');
			this.distributorInputFocused = false;
			// 延迟隐藏，给点击选择留出时间
			setTimeout(() => {
				this.showDistributorList = false;
			}, 200);
		},

		// 点击分销商输入框区域聚焦
		focusDistributorInputBox() {
			console.log('点击分销商输入框区域');
			this.distributorInputFocused = true;
			this.showDistributorList = true;
			// 如果没有搜索结果，显示默认列表
			if (this.distributorSearchResults.length === 0) {
				this.distributorSearchResults = this.businessDistributorList.slice(0, 10);
			}
		},

		// 加载工作轨迹
		async loadWorkTrack() {
			try {
				// 获取有经纬度的拜访记录，按时间排序
				const validVisits = this.pointList
					.filter(visit => visit.longitude && visit.latitude)
					.sort((a, b) => {
						const timeA = new Date(`${a.visitDate || new Date().toISOString().split('T')[0]} ${a.visitTime || '00:00'}`);
						const timeB = new Date(`${b.visitDate || new Date().toISOString().split('T')[0]} ${b.visitTime || '00:00'}`);
						return timeA - timeB;
					});

				if (validVisits.length < 2) {
					// 少于2个点无法计算路径距离
					this.trackStats.distance = '0.0';
					this.trackStats.duration = '0.0';
					return;
				}

				// 方法一：使用几何计算折线距离（推荐，参考高德示例）
				// const totalDistance = this.calculatePolylineDistance(validVisits);

				// 方法二：如果需要精确的道路距离，可以调用路径规划API（可选）
				const totalDistance = await this.calculateRoadDistanceWithAPI(validVisits);
				console.log("🚀 ~ loadWorkTrack ~ totalDistance:", totalDistance)

				// 计算实际工作时长（使用pointList中的createTime）
				const totalDuration = this.calculateWorkDuration(this.pointList);

				// 更新轨迹统计
				this.trackStats.distance = (totalDistance / 1000).toFixed(1); // 转换为公里
				this.trackStats.duration = totalDuration.toFixed(1); // 小时

				console.log(`工作轨迹统计: 距离${this.trackStats.distance}km, 时长${this.trackStats.duration}h`);

			} catch (error) {
				console.error('加载工作轨迹失败:', error);
				this.trackStats.distance = '0.0';
				this.trackStats.duration = '0.0';
			}
		},

		// 方法一：使用几何计算折线距离（参考高德地图示例）
		calculatePolylineDistance(visits) {
			if (visits.length < 2) return 0;

			let totalDistance = 0;

			// 参考高德示例：逐段计算直线距离并累加
			// 类似于 AMap.GeometryUtil.distanceOfLine(arr) 的实现原理
			for (let i = 0; i < visits.length - 1; i++) {
				const point1 = visits[i];
				const point2 = visits[i + 1];

				// 使用Haversine公式计算两点间距离
				const segmentDistance = this.calculateStraightDistance(point1, point2);
				totalDistance += segmentDistance.distance;
			}

			return totalDistance;
		},

		// 方法二：调用高德API获取精确道路距离（可选，适用于需要真实路径的场景）
		async calculateRoadDistanceWithAPI(visits) {
			if (visits.length < 2) return 0;

			// 构建途经点参数（中间的所有点）
			const waypoints = visits.slice(1, -1).map(visit =>
				`${visit.longitude},${visit.latitude}`
			).join(';');

			const origin = `${visits[0].longitude},${visits[0].latitude}`;
			const destination = `${visits[visits.length - 1].longitude},${visits[visits.length - 1].latitude}`;

			// 构建API请求参数
			const params = {
				origin: origin,
				destination: destination,
				key: this.amapKey,
				output: 'json'
			};

			// 如果有途经点，添加waypoints参数
			if (waypoints) {
				params.waypoints = waypoints;
			}

			const queryString = Object.keys(params)
				.map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
				.join('&');

			const response = await uni.request({
				url: `https://restapi.amap.com/v3/direction/driving?${queryString}`,
				method: 'GET',
				timeout: 10000
			});
			if (response[1] && response[1].statusCode == 200) {
				return response[1].data.route.paths[0].distance;
			} else {
				return this.calculatePolylineDistance(visits);
			}
		},

		// 调用高德API计算两点间距离
		async calculateDistance(origin, destination) {
			try {
				const originCoord = `${origin.longitude},${origin.latitude}`;
				const destinationCoord = `${destination.longitude},${destination.latitude}`;

				// 构建高德API请求URL
				const apiUrl = `https://restapi.amap.com/v4/direction/driving`;
				const params = {
					origin: originCoord,
					destination: destinationCoord,
					key: this.amapKey,
					output: 'json'
				};

				// 构建查询字符串
				const queryString = Object.keys(params)
					.map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
					.join('&');

				const fullUrl = `${apiUrl}?${queryString}`;

				console.log('调用高德API:', fullUrl);

				// 发起请求
				const response = await uni.request({
					url: fullUrl,
					method: 'GET',
					timeout: 10000
				});

				console.log('高德API响应:', response);

				if (response.statusCode === 200 && response.data.status === '1') {
					const route = response.data.route;
					if (route && route.paths && route.paths.length > 0) {
						const path = route.paths[0];
						return {
							distance: parseInt(path.distance) || 0, // 距离，单位：米
							duration: parseInt(path.duration) || 0   // 时长，单位：秒
						};
					}
				} else {
					console.error('高德API返回错误:', response.data);
					// 如果API调用失败，使用直线距离估算
					return this.calculateStraightDistance(origin, destination);
				}
			} catch (error) {
				console.error('调用高德API失败:', error);
				// 如果API调用失败，使用直线距离估算
				return this.calculateStraightDistance(origin, destination);
			}
			return null;
		},

		// 计算两点间直线距离（备用方案）
		calculateStraightDistance(origin, destination) {
			const lat1 = parseFloat(origin.latitude);
			const lon1 = parseFloat(origin.longitude);
			const lat2 = parseFloat(destination.latitude);
			const lon2 = parseFloat(destination.longitude);

			// 使用Haversine公式计算球面距离
			const R = 6371000; // 地球半径，单位：米
			const dLat = this.toRadians(lat2 - lat1);
			const dLon = this.toRadians(lon2 - lon1);
			const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
				Math.cos(this.toRadians(lat1)) * Math.cos(this.toRadians(lat2)) *
				Math.sin(dLon / 2) * Math.sin(dLon / 2);
			const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
			const distance = R * c;

			// 估算行驶时间（假设平均速度30km/h）
			const duration = (distance / 1000) * 3600 / 30;

			return {
				distance: Math.round(distance),
				duration: Math.round(duration)
			};
		},

		// 角度转弧度
		toRadians(degrees) {
			return degrees * (Math.PI / 180);
		},

		// 计算实际工作时长（使用pointList中的createTime）
		calculateWorkDuration(pointList) {
			if (!pointList || pointList.length < 2) {
				return 0;
			}

			try {
				// 按createTime排序，确保时间顺序正确
				const sortedPoints = [...pointList].sort((a, b) => {
					const timeA = new Date(a.createTime);
					const timeB = new Date(b.createTime);
					return timeA - timeB;
				});

				// 获取第一条和最后一条记录的时间
				const startTime = new Date(sortedPoints[0].createTime);
				const endTime = new Date(sortedPoints[sortedPoints.length - 1].createTime);

				// 计算时间差（毫秒）
				const timeDiffMs = endTime - startTime;

				// 转换为小时
				const timeDiffHours = timeDiffMs / (1000 * 60 * 60);

				console.log(`工作时长计算: 开始时间${sortedPoints[0].createTime}, 结束时间${sortedPoints[sortedPoints.length - 1].createTime}, 时长${timeDiffHours.toFixed(2)}小时`);

				return Math.max(0, timeDiffHours); // 确保不返回负数

			} catch (error) {
				console.error('计算工作时长失败:', error);
				return 0;
			}
		},

		// 添加拜访地点标记
		async addVisitLocationMarkers() {
			// 清除之前的拜访地点标记
			this.mapMarkers = this.mapMarkers.filter(marker =>
				!marker.id.toString().startsWith('visit_')
			);
			const response = await this.$http.get('/pm/visit/plan/visitRecords');

			this.pointList = response.data.result;
			this.mapCenter.longitude = response.data.result[0].longitude;
			this.mapCenter.latitude = response.data.result[0].latitude;
			this.loadWorkTrack();
			response.data.result.forEach((visit) => {
				if (visit.longitude && visit.latitude) {
					const visitMarker = {
						id: `visit_${visit.id}`,
						longitude: parseFloat(visit.longitude),
						latitude: parseFloat(visit.latitude),
						iconPath: '/static/images/icon.png',
						width: 16,
						height: 16,
						title: visit.customerName,

					};

					this.mapMarkers.push(visitMarker);
				}
			});
			// 更新轨迹统计
			this.trackStats.locations = this.mapMarkers.length;
		},
		// 生成拜访路线连线
		generateVisitPolyline() {
			// 获取已完成的拜访点，按时间排序
			const completedVisits = this.pointList
				.filter(visit => visit.longitude && visit.latitude)
				.sort((a, b) => {
					// 按拜访时间排序
					const timeA = new Date(`${a.visitDate || new Date().toISOString().split('T')[0]} ${a.visitTime || '00:00'}`);
					const timeB = new Date(`${b.visitDate || new Date().toISOString().split('T')[0]} ${b.visitTime || '00:00'}`);
					return timeA - timeB;
				});

			if (completedVisits.length < 2) {
				// 少于2个点无法连线
				this.mapPolyline = [];
				return;
			}

			// 生成连线坐标点
			const points = completedVisits.map(visit => ({
				longitude: parseFloat(visit.longitude),
				latitude: parseFloat(visit.latitude)
			}));

			// 创建连线配置
			this.mapPolyline = [{
				points: points,
				color: '#FF6B35', // 橙色连线
				width: 3, // 增加线宽，更容易看到
				dottedLine: false,
				arrowLine: true, // 显示箭头表示方向
				borderColor: '#FFFFFF',
				borderWidth: 2
			}];

		},

		// 获取状态文本
		getStatusText(status) {
			return this.statusMap[status]?.text || '未知';
		},

		// 获取状态样式类
		getStatusClass(status) {
			return this.statusMap[status]?.class || '';
		},

		// 查看拜访详情
		viewVisitDetail(visit) {
			console.log("🚀 ~ viewVisitDetail ~ visit:", visit)
			uni.navigateTo({
				url: `/pages/customerVisit/visitDetail?id=${visit.id}`
			});
		},

		// 开始拜访
		async startVisit(visit) {
			try {
				const response = await this.$http.post(`/pm/visit/plan/start/${visit.id}`);
				if (response.data.success) {
					this.$tip.success('拜访已开始');
					await this.refreshData();
				} else {
					this.$tip.error(response.data.message || '操作失败');
				}
			} catch (error) {
				console.error('开始拜访失败:', error);
				this.$tip.error('操作失败');
			}
		},

		// 完成拜访
		async finishVisit(visit) {
			try {
				const response = await this.$http.post(`/pm/visit/plan/finish/${visit.id}`);
				if (response.data.success) {
					this.$tip.success('拜访已完成');
					await this.refreshData();
				} else {
					this.$tip.error(response.data.message || '操作失败');
				}
			} catch (error) {
				console.error('完成拜访失败:', error);
				this.$tip.error('操作失败');
			}
		},

		// 刷新轨迹
		refreshTrack() {
			this.addVisitLocationMarkers();
			this.generateVisitPolyline();
			this.$tip.success('轨迹已刷新');
		},

		// 显示新增弹窗
		showAddVisitModal() {
			console.log('显示新增弹窗，当前拜访目的列表:', this.visitPurposeList);
			this.resetForm();
			this.showAddModal = true;
			// 防止页面滚动
			this.preventScroll();
		},

		// 隐藏新增弹窗
		hideAddModal() {
			this.showAddModal = false;
			this.resetForm();
			// 恢复页面滚动
			this.allowScroll();
		},

		// 防止页面滚动
		preventScroll() {
			// 在uni-app中，可以通过设置页面样式来防止滚动
			try {
				const pages = getCurrentPages();
				const currentPage = pages[pages.length - 1];
				if (currentPage && currentPage.$el) {
					currentPage.$el.style.overflow = 'hidden';
				}
			} catch (error) {
				console.log('防止滚动设置失败:', error);
			}
		},

		// 允许页面滚动
		allowScroll() {
			try {
				const pages = getCurrentPages();
				const currentPage = pages[pages.length - 1];
				if (currentPage && currentPage.$el) {
					currentPage.$el.style.overflow = 'auto';
				}
			} catch (error) {
				console.log('恢复滚动设置失败:', error);
			}
		},

		// 重置表单
		resetForm() {
			this.visitForm = {
				customerCode: '',
				customerName: '',
				visitDate: '',
				visitTime: '',
				visitPurpose: '',
				purposeCodes: '',
				distributorName: '', // 重置分销商名称
				systemName: '', // 重置体系名称
			};

			// 重置客户类型选择
			this.selectedCustomerType = null;

			// 重置客户相关数据
			this.selectedCustomer = null;
			this.customerSearchKeyword = '';
			this.customerSearchResults = this.customerList.slice(0, 10);
			this.showCustomerList = false;
			this.inputFocused = false;
			this.selectedPurposes = []; // 重置多选数组

			// 重置体系相关数据
			this.selectedSystem = null;
			this.systemSearchKeyword = '';
			this.systemSearchResults = [];
			this.showSystemList = false;
			this.systemInputFocused = false;

			// 重置分销商相关数据
			this.selectedDistributor = null;
			this.distributorSearchKeyword = '';
			this.distributorSearchResults = [];
			this.showDistributorList = false;
			this.distributorInputFocused = false;

			// 重置列表数据
			this.businessDistributorList = [];
			this.businessSystemBaseList = [];
		},

		// 日期选择
		onDateChange(e) {
			this.visitForm.visitDate = e.detail.value;
			console.log('选择日期，visitForm已更新:', this.visitForm);
		},

		// 时间选择
		onTimeChange(e) {
			this.visitForm.visitTime = e.detail.value;
			console.log('选择时间，visitForm已更新:', this.visitForm);
		},

		// 切换拜访目的选择
		togglePurpose(purpose) {
			const purposeCode = purpose.purposeCode;
			const currentIndex = this.selectedPurposes.indexOf(purposeCode);

			if (currentIndex > -1) {
				// 如果已选中，则取消选择
				this.selectedPurposes.splice(currentIndex, 1);
			} else {
				// 如果未选中，则添加选择
				this.selectedPurposes.push(purposeCode);
			}

			// 更新表单数据
			this.updateVisitFormPurposes();
		},

		// 更新表单中的拜访目的数据
		updateVisitFormPurposes() {
			if (this.selectedPurposes.length > 0) {
				// 获取选中的目的名称
				const selectedPurposeNames = this.selectedPurposes.map(code => {
					const purpose = this.visitPurposeList.find(p => p.purposeCode === code);
					return purpose ? purpose.purposeName : '';
				}).filter(name => name);

				// 将选中的目的存储到表单中
				this.visitForm.visitPurpose = selectedPurposeNames.join(',');
				this.visitForm.purposeCodes = this.selectedPurposes.join(',');
			} else {
				this.visitForm.visitPurpose = '';
				this.visitForm.purposeCodes = '';
			}

			console.log('更新拜访目的，visitForm已更新:', this.visitForm);
		},

		// 获取客户类型在列表中的索引
		getCustomerTypeIndex() {
			if (!this.selectedCustomerType) return 0;
			const index = this.customerTypeList.findIndex(item => item.code === this.selectedCustomerType.code);
			return index >= 0 ? index : 0;
		},

		// 客户类型选择变化
		onCustomerTypeChange(e) {
			const index = e.detail.value;
			if (index >= 0 && index < this.customerTypeList.length) {
				this.selectedCustomerType = this.customerTypeList[index];
				console.log('选择客户类型:', this.selectedCustomerType);
				this.visitForm.type = this.selectedCustomerType.code;
				// 清空之前的选择
				this.clearAllSelections();

				// 根据客户类型加载对应的数据
				this.loadDataByCustomerType();
			} else {
				this.selectedCustomerType = null;
				this.clearAllSelections();
			}
		},

		// 清空所有选择
		clearAllSelections() {
			// 清空客户选择
			this.selectedCustomer = null;
			this.customerSearchKeyword = '';
			this.showCustomerList = false;
			this.inputFocused = false;

			// 清空体系选择
			this.selectedSystem = null;
			this.systemSearchKeyword = '';
			this.showSystemList = false;
			this.systemInputFocused = false;

			// 清空分销商选择
			this.selectedDistributor = null;
			this.distributorSearchKeyword = '';
			this.showDistributorList = false;
			this.distributorInputFocused = false;

			// 清空表单相关字段
			this.visitForm.customerCode = '';
			this.visitForm.customerName = '';
			this.visitForm.distributorName = '';
			this.visitForm.systemName = '';
		},

		// 根据客户类型加载对应的数据
		async loadDataByCustomerType() {
			if (!this.selectedCustomerType) return;

			try {
				if (this.selectedCustomerType.code === '客户') {
					// 加载客户列表
					await this.loadCustomerList();
				} else if (this.selectedCustomerType.code === '体系') {
					// 加载体系列表
					await this.loadSystemList();
				} else if (this.selectedCustomerType.code === '分销商') {
					// 加载分销商列表
					await this.loadDistributorList();
				}
			} catch (error) {
				console.error('加载数据失败:', error);
			}
		},

		// 加载体系列表
		async loadSystemList() {
			try {
				const response = await this.$http.get('/pm/visit/plan/getChoiceList', {
					params: {
						type: '体系'
					}
				});
				if (response.data.success) {
					this.businessSystemBaseList = response.data.result || [];
					this.systemSearchResults = this.businessSystemBaseList.slice(0, 10); // 默认显示前10个
				}
			} catch (error) {
				console.error('加载体系列表失败:', error);
				this.businessSystemBaseList = [];
				this.systemSearchResults = [];
			}
		},

		// 加载分销商列表
		async loadDistributorList() {
			try {
				const response = await this.$http.get('/pm/visit/plan/getChoiceList', {
					params: {
						type: '分销商'
					}
				});
				if (response.data.success) {
					this.businessDistributorList = response.data.result || [];
					this.distributorSearchResults = this.businessDistributorList.slice(0, 10); // 默认显示前10个
				}
			} catch (error) {
				console.error('加载分销商列表失败:', error);
				this.businessDistributorList = [];
				this.distributorSearchResults = [];
			}
		},



		// 提交拜访计划
		async submitVisit() {
			// 表单验证
			if (!this.selectedCustomerType) {
				this.$tip.error('请选择客户类型');
				return;
			}

			// 根据客户类型验证对应的选择
			if (this.selectedCustomerType.code === '客户') {
				if (!this.selectedCustomer || !this.visitForm.customerName) {
					this.$tip.error('请选择客户');
					return;
				}
			} else if (this.selectedCustomerType.code === '体系') {
				if (!this.selectedSystem || !this.visitForm.systemName) {
					this.$tip.error('请选择体系');
					return;
				}
			} else if (this.selectedCustomerType.code === '分销商') {
				if (!this.selectedDistributor || !this.visitForm.distributorName) {
					this.$tip.error('请选择分销商');
					return;
				}
			} else if (this.selectedCustomerType.code === '意向客户') {
				if (!this.selectedDistributor || !this.visitForm.customerName) {
					this.$tip.error('请填写意向客户');
					return;
				}
			}
			if (!this.visitForm.visitDate) {
				this.$tip.error('请选择拜访日期');
				return;
			}
			if (!this.visitForm.visitTime) {
				this.$tip.error('请选择拜访时间');
				return;
			}
			/* if (!this.visitForm.visitAddress) {
				this.$tip.error('请选择拜访地点');
				return;
			} */
			if (this.selectedPurposes.length === 0) {
				this.$tip.error('请选择拜访目的');
				return;
			}

			this.submitting = true;

			try {
				const response = await this.$http.post('/pm/visit/plan/add', this.visitForm);
				if (response.data.success) {
					this.$tip.success('拜访计划创建成功');
					this.hideAddModal();
					await this.refreshData();
				} else {
					this.$tip.error(response.data.message || '创建失败');
				}
			} catch (error) {
				console.error('创建拜访计划失败:', error);
				this.$tip.error('创建失败');
			} finally {
				this.submitting = false;
			}
		},
	}
}
</script>

<style lang="scss" scoped>
.customer-visit-container {
	background-color: #f5f7fa;
	min-height: 100vh;
	position: relative;
	z-index: 1;
	overflow-x: hidden;
}

.main-content {
	padding: 20rpx;
}

/* 今日状态卡片 */
.status-card {
	background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
	color: white;
	border-radius: 24rpx;
	padding: 40rpx;
	margin-bottom: 30rpx;
	box-shadow: 0 8rpx 30rpx rgba(79, 172, 254, 0.3);
}

.status-title {
	font-size: 32rpx;
	font-weight: 600;
	margin-bottom: 30rpx;
	display: flex;
	align-items: center;
}

.status-content {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 20rpx;
}

.status-item {
	background: rgba(255, 255, 255, 0.2);
	border-radius: 20rpx;
	padding: 24rpx;
	text-align: center;
	backdrop-filter: blur(10rpx);
}

.status-value {
	font-size: 40rpx;
	font-weight: bold;
	margin-bottom: 8rpx;
}

.status-label {
	font-size: 24rpx;
	opacity: 0.9;
}

/* 卡片样式 */
.card {
	background: white;
	border-radius: 24rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
	margin-bottom: 30rpx;
	overflow: hidden;
}

.card-header {
	padding: 30rpx;
	border-bottom: 1px solid #f0f0f0;
	display: flex;
	align-items: center;
	justify-content: space-between;
	font-weight: 600;
	font-size: 32rpx;
}

.card-body {
	padding: 30rpx;
}

/* 空状态 */
.empty-state {
	text-align: center;
	padding: 60rpx 0;
	color: #999;
}

.empty-state .cuIcon-calendar {
	font-size: 80rpx;
	margin-bottom: 20rpx;
	display: block;
}

.empty-text {
	font-size: 28rpx;
}

/* 拜访项目 */
.visit-item {
	display: flex;
	align-items: flex-start;
	padding: 24rpx 0;
	border-bottom: 1px solid #f0f0f0;

	&:last-child {
		border-bottom: none;
	}
}

.visit-avatar {
	width: 80rpx;
	height: 80rpx;
	border-radius: 20rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	display: flex;
	align-items: center;
	justify-content: center;
	color: white;
	font-weight: bold;
	font-size: 32rpx;
	margin-right: 24rpx;
	flex-shrink: 0;
}

.visit-info {
	flex: 1;
	min-width: 0;
}

.visit-name {
	font-weight: 600;
	font-size: 30rpx;
	margin-bottom: 8rpx;
}

.visit-meta {
	display: flex;
	align-items: center;
	font-size: 24rpx;
	color: #666;
	margin-bottom: 8rpx;
}

.visit-purpose {
	font-size: 24rpx;
	color: #999;
}

/* 业务信息样式 */
.visit-business-info {
	font-size: 22rpx;
	color: #666;
}

.business-item {
	display: flex;
	align-items: center;
	margin-bottom: 4rpx;
}

.business-label {
	color: #666;
	margin-right: 8rpx;
	font-weight: 500;
}

.business-value {
	color: #333;
	font-weight: 400;
}

.visit-actions {
	display: flex;
	flex-direction: column;
	align-items: flex-end;
	margin-left: 20rpx;
}

.visit-status {
	font-size: 22rpx;
	padding: 6rpx 16rpx;
	border-radius: 20rpx;
	font-weight: 500;
}

.status-planned {
	background: #e3f2fd;
	color: #1976d2;
}

.status-progress {
	background: #e8f5e9;
	color: #388e3c;
}

.status-completed {
	background: #f3e5f5;
	color: #7b1fa2;
}

.status-canceled {
	background: #ffebee;
	color: #d32f2f;
}

.action-buttons {
	margin-top: 12rpx;
}

/* 添加按钮 */
.add-button {
	display: flex;
	align-items: center;
	justify-content: center;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
	border: none;
	border-radius: 20rpx;
	padding: 24rpx 40rpx;
	font-weight: 600;
	font-size: 28rpx;
	box-shadow: 0 8rpx 20rpx rgba(102, 126, 234, 0.3);
}

/* 地图容器 */
.map-container {
	height: 400rpx;
	border-radius: 20rpx;
	overflow: hidden;
	background: #f5f5f5;
	margin-bottom: 30rpx;
}

.work-map {
	width: 100%;
	height: 100%;
}

/* 地图控制按钮 */
.map-controls {
	position: absolute;
	right: 20rpx;
	top: 20rpx;
	display: flex;
	flex-direction: column;
	gap: 12rpx;
	z-index: 10;
}

.control-btn {
	width: 60rpx;
	height: 60rpx;
	background: rgba(0, 0, 0, 0.7);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	backdrop-filter: blur(10rpx);
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.3);
	transition: all 0.3s ease;

	&:active {
		transform: scale(0.95);
		background: rgba(0, 0, 0, 0.8);
	}

	.cuIcon-refresh,
	.cuIcon-group,
	.cuIcon-list,
	.cuIcon-link {
		font-size: 28rpx;
	}
}

/* 轨迹信息 */
.track-info {
	display: flex;
	justify-content: space-around;
	background: #f8f9fa;
	border-radius: 20rpx;
	padding: 30rpx 0;
}

.track-data {
	text-align: center;
	flex: 1;
}

.track-value {
	font-size: 36rpx;
	font-weight: bold;
	color: #ff6b35;
	margin-bottom: 8rpx;
}

.track-label {
	color: #666;
	font-size: 24rpx;
}

/* 弹窗样式 */
.modal-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.6);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 99999;
	padding: 40rpx;
	backdrop-filter: blur(4rpx);
}

.modal-content {
	background: white;
	border-radius: 24rpx;
	width: 100%;
	max-width: 600rpx;
	max-height: 80vh;
	display: flex;
	flex-direction: column;
	overflow: hidden;
	box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3);
	transform: scale(1);
	animation: modalShow 0.3s ease-out;
}

.modal-header {
	padding: 40rpx;
	border-bottom: 1px solid #f0f0f0;
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.modal-title {
	font-size: 36rpx;
	font-weight: 600;
}

.modal-body {
	flex: 1;
	padding: 40rpx;
	overflow-y: auto;
}

.modal-footer {
	padding: 30rpx 40rpx;
	border-top: 1px solid #f0f0f0;
	display: flex;
	gap: 20rpx;
}

.modal-footer .cu-btn {
	flex: 1;
}

/* 表单样式 */
.form-group {
	margin-bottom: 30rpx;
}

.form-label {
	display: block;
	font-size: 28rpx;
	font-weight: 500;
	margin-bottom: 16rpx;
	color: #333;
}

.form-input,
.form-textarea {
	width: 100%;
	padding: 24rpx;
	border: 1px solid #e0e0e0;
	border-radius: 12rpx;
	font-size: 28rpx;
	background: #fafafa;
}

.form-textarea {
	min-height: 120rpx;
	resize: none;
}

.picker-input {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 24rpx;
	border: 1px solid #e0e0e0;
	border-radius: 12rpx;
	background: #fafafa;
	font-size: 28rpx;
}

/* 客户搜索组件样式 */
.customer-search-container {
	position: relative;
}

.search-input-wrapper {
	position: relative;
	width: 100%;
}

.customer-search-input {
	width: 100%;
	padding: 24rpx 60rpx 24rpx 24rpx;
	border: 1px solid #e0e0e0;
	border-radius: 12rpx;
	font-size: 28rpx;
	background: white;
	color: #333;
	box-sizing: border-box;
	outline: none;
	-webkit-appearance: none;

	&:focus {
		border-color: #007aff;
		background: white;
	}
}

.search-icon {
	position: absolute;
	right: 24rpx;
	top: 50%;
	transform: translateY(-50%);
	font-size: 32rpx;
}

.customer-list-dropdown {
	position: absolute;
	top: 100%;
	left: 0;
	right: 0;
	background: white;
	border: 1px solid #e0e0e0;
	border-top: none;
	border-radius: 0 0 12rpx 12rpx;
	max-height: 400rpx;
	overflow-y: auto;
	z-index: 1000;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.customer-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 24rpx;
	border-bottom: 1px solid #f0f0f0;

	&:last-child {
		border-bottom: none;
	}

	&:hover {
		background: #f8f9fa;
	}
}

.customer-info {
	flex: 1;
}

.customer-name {
	font-size: 28rpx;
	font-weight: 500;
	margin-bottom: 4rpx;
	display: block;
}

.customer-code {
	font-size: 24rpx;
	color: #666;
	display: block;
}

.customer-type {
	font-size: 22rpx;
	padding: 4rpx 12rpx;
	border-radius: 12rpx;
	background: #e3f2fd;
	color: #1976d2;
}

.no-results {
	padding: 40rpx;
	text-align: center;
	color: #999;
	font-size: 26rpx;
}

.selected-customer {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 24rpx;
	border: 1px solid #007aff;
	border-radius: 12rpx;
	background: #f0f8ff;
	margin-top: 16rpx;
}

.selected-info {
	flex: 1;
}

.selected-name {
	font-size: 28rpx;
	font-weight: 500;
	color: #007aff;
	margin-bottom: 4rpx;
	display: block;
}

.selected-code {
	font-size: 24rpx;
	color: #666;
	display: block;
}

/* 弹窗动画 */
@keyframes modalShow {
	0% {
		opacity: 0;
		transform: scale(0.8) translateY(50rpx);
	}

	100% {
		opacity: 1;
		transform: scale(1) translateY(0);
	}
}

/* 确保弹窗在最顶层 */
.customer-visit-container {
	position: relative;
	z-index: 1;
}

/* 修复可能的层级问题 */
.cu-custom {
	z-index: 999 !important;
}

.modal-overlay {
	z-index: 999999 !important;
	position: fixed !important;
	top: 0 !important;
	left: 0 !important;
	right: 0 !important;
	bottom: 0 !important;
	width: 100vw !important;
	height: 100vh !important;
	background: rgba(0, 0, 0, 0.7) !important;
}

/* 确保弹窗内容居中 */
.modal-content {
	margin: auto;
	position: relative;
	z-index: 100000;
}

/* 防止弹窗下方内容滚动 */
.modal-overlay {
	display: flex !important;
	align-items: center !important;
	justify-content: center !important;
}

/* 拜访目的多选样式 */
.purpose-checkbox-container {
	border: 1px solid #e0e0e0;
	border-radius: 12rpx;
	background: #fafafa;
	padding: 20rpx;
	min-height: 100rpx;
}

.purpose-checkbox-item {
	margin-bottom: 20rpx;

	&:last-child {
		margin-bottom: 0;
	}
}

.checkbox-wrapper {
	display: flex;
	align-items: center;
	padding: 12rpx 0;
}

.checkbox {
	width: 40rpx;
	height: 40rpx;
	border: 2rpx solid #ddd;
	border-radius: 8rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 20rpx;
	transition: all 0.3s ease;

	&.checked {
		background: #007aff;
		border-color: #007aff;
	}
}

.purpose-text {
	font-size: 28rpx;
	color: #333;
	flex: 1;
}

.purpose-placeholder {
	color: #999;
	font-size: 28rpx;
	padding: 24rpx 0;
	text-align: center;
}

/* 表单输入框焦点样式 */
.form-input:focus,
.form-textarea:focus,
.picker-input:active {
	border-color: #007aff;
	background: white;
}
</style>
